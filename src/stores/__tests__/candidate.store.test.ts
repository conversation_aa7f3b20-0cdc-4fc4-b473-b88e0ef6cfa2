import { describe, it, expect, vi, beforeEach } from "vitest";
import { renderHook, act } from "@testing-library/react";
import { useCandidateStore } from "../candidate.store";
import { mockCandidates } from "../../test/mocks/data";

// Mock the service container
const mockCandidateService = {
  fetchCandidates: vi.fn(),
  updateCandidateStatus: vi.fn(),
  updateCandidateRating: vi.fn(),
  fetchApplicationLogs: vi.fn(),
  addApplicationLog: vi.fn(),
};

vi.mock("../../services", () => ({
  services: {
    candidate: mockCandidateService,
  },
}));

describe("useCandidateStore", () => {
  beforeEach(() => {
    // Reset store state before each test
    useCandidateStore.setState({
      candidates: [],
      applicationLogs: [],
      filters: { position: "", location: "", status: "" },
      loading: false,
      error: null,
      recoveryActions: [],
      lastFailedAction: null,
    });

    // Reset all mocks
    vi.clearAllMocks();
  });

  it("initializes with default state", () => {
    const { result } = renderHook(() => useCandidateStore());

    expect(result.current.candidates).toEqual([]);
    expect(result.current.applicationLogs).toEqual([]);
    expect(result.current.filters).toEqual({
      position: "",
      location: "",
      status: "",
    });
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it("fetches candidates successfully", async () => {
    mockCandidateService.fetchCandidates.mockResolvedValue(mockCandidates);

    const { result } = renderHook(() => useCandidateStore());

    await act(async () => {
      await result.current.fetchCandidates();
    });

    expect(result.current.candidates).toEqual(mockCandidates);
    expect(result.current.loading).toBe(false);
    expect(result.current.error).toBeNull();
  });

  it("handles fetch candidates error", async () => {
    const errorMessage = "Failed to fetch candidates";
    mockCandidateService.fetchCandidates.mockRejectedValue(
      new Error(errorMessage)
    );

    const { result } = renderHook(() => useCandidateStore());

    await act(async () => {
      await result.current.fetchCandidates();
    });

    expect(result.current.candidates).toEqual([]);
    expect(result.current.loading).toBe(false);
    // Error is now an AppError object, not a string
    expect(result.current.error).toMatchObject({
      message: errorMessage,
      category: expect.any(String),
      code: expect.any(String),
      userMessage: expect.any(String),
    });
    expect(result.current.recoveryActions).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          strategy: expect.any(String),
          label: expect.any(String),
        }),
      ])
    );
  });

  it("updates candidate status", async () => {
    mockCandidateService.updateCandidateStatus.mockResolvedValue();
    mockCandidateService.addApplicationLog.mockResolvedValue();
    mockCandidateService.fetchCandidates.mockResolvedValue(mockCandidates);

    const { result } = renderHook(() => useCandidateStore());

    await act(async () => {
      await result.current.updateCandidateStatus("1", "in_progress");
    });

    expect(mockCandidateService.updateCandidateStatus).toHaveBeenCalledWith(
      "1",
      "in_progress"
    );
    expect(mockCandidateService.addApplicationLog).toHaveBeenCalledWith(
      "1",
      "in_progress",
      "Status updated to in_progress"
    );
  });

  it("updates filters", () => {
    const { result } = renderHook(() => useCandidateStore());

    act(() => {
      result.current.setFilters({ position: "1", status: "new" });
    });

    expect(result.current.filters).toEqual({
      position: "1",
      location: "",
      status: "new",
    });
  });

  it("clears errors", () => {
    const { result } = renderHook(() => useCandidateStore());

    // Set an error first (now using AppError structure)
    const mockError = {
      message: "Some error",
      category: "NETWORK_ERROR",
      code: "FETCH_FAILED",
      userMessage: "Failed to load data",
    };

    act(() => {
      useCandidateStore.setState({
        error: mockError,
        recoveryActions: [
          { strategy: "retry", label: "Retry", action: () => {} },
        ],
      });
    });

    expect(result.current.error).toEqual(mockError);

    act(() => {
      result.current.clearError();
    });

    expect(result.current.error).toBeNull();
    expect(result.current.recoveryActions).toEqual([]);
  });

  it("updates candidate rating", async () => {
    mockCandidateService.updateCandidateRating.mockResolvedValue();
    mockCandidateService.fetchCandidates.mockResolvedValue(mockCandidates);

    const { result } = renderHook(() => useCandidateStore());

    await act(async () => {
      await result.current.updateCandidateRating("1", 5);
    });

    expect(mockCandidateService.updateCandidateRating).toHaveBeenCalledWith(
      "1",
      5
    );
    expect(mockCandidateService.fetchCandidates).toHaveBeenCalled();
  });
});
